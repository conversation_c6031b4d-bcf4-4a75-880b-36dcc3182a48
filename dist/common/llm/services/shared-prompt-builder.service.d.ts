import { ConversationContext } from '../../../chat/interfaces/conversation-context.interface';
import { CandidateEntity } from '../interfaces/llm.service.interface';
export declare class SharedPromptBuilderService {
    getBaseSystemRules(): string[];
    buildSystemPrompt(context: ConversationContext, candidateEntities?: CandidateEntity[], additionalRules?: string[]): string;
    buildConversationHistory(context: ConversationContext): Array<{
        role: string;
        content: string;
    }>;
    buildUserMessage(userMessage: string, context: ConversationContext): string;
    validateResponseEntities(response: string, candidateEntities: CandidateEntity[]): {
        isValid: boolean;
        issues: string[];
    };
    formatEntitiesForPrompt(entities: CandidateEntity[]): string;
    getStageInstructions(stage: string, hasEntities: boolean): string;
}
