{"version": 3, "file": "shared-prompt-builder.service.js", "sourceRoot": "", "sources": ["../../../../src/common/llm/services/shared-prompt-builder.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAKrC,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IAKrC,kBAAkB;QAChB,OAAO;YACL,8FAA8F;YAC9F,mEAAmE;YACnE,6FAA6F;YAC7F,qFAAqF;YACrF,gFAAgF;YAChF,+DAA+D;YAC/D,kDAAkD;SACnD,CAAC;IACJ,CAAC;IAKD,iBAAiB,CACf,OAA4B,EAC5B,iBAAqC,EACrC,kBAA4B,EAAE;QAE9B,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5C,MAAM,QAAQ,GAAG,CAAC,GAAG,SAAS,EAAE,GAAG,eAAe,CAAC,CAAC;QAEpD,IAAI,YAAY,GAAG;;;EAGrB,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;mBAGhD,OAAO,CAAC,iBAAiB;gBAC5B,OAAO,CAAC,SAAS;mBACd,OAAO,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC;QAG/C,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,YAAY,IAAI;;+BAES,iBAAiB,CAAC,MAAM;EACrD,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CACxC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,EAAE;aACvC,MAAM,CAAC,IAAI;oBACJ,MAAM,CAAC,gBAAgB;mBACxB,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,SAAS;iBACtE,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,kBAAkB,EAAE,CAC5F,CAAC,IAAI,CAAC,MAAM,CAAC;;iBAEG,iBAAiB,CAAC,MAAM,6FAA6F,CAAC;QACnI,CAAC;aAAM,CAAC;YACN,YAAY,IAAI;;;yHAGmG,CAAC;QACtH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAKD,wBAAwB,CAAC,OAA4B;QACnD,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAClC,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC,CAAC;IACN,CAAC;IAKD,gBAAgB,CAAC,WAAmB,EAAE,OAA4B;QAGhE,OAAO,WAAW,CAAC;IACrB,CAAC;IAKD,wBAAwB,CAAC,QAAgB,EAAE,iBAAoC;QAI7E,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAC9B,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAC3D,CAAC;QAGF,MAAM,kBAAkB,GAAG;YACzB,wHAAwH;YACxH,4GAA4G;SAC7G,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,kBAAkB,EAAE,CAAC;YACzC,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;oBAC5B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;wBAC/C,MAAM,CAAC,IAAI,CAAC,6CAA6C,KAAK,GAAG,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAKD,uBAAuB,CAAC,QAA2B;QACjD,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,OAAO,yCAAyC,CAAC;QACnD,CAAC;QAED,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpC,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC;YACxF,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC;YAE5F,OAAO,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI;oBACvB,MAAM,CAAC,gBAAgB;mBACxB,UAAU;iBACZ,QAAQ;gBACT,MAAM,CAAC,UAAU,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAKD,oBAAoB,CAAC,KAAa,EAAE,WAAoB;QACtD,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,UAAU;gBACb,OAAO,WAAW;oBAChB,CAAC,CAAC,wEAAwE;oBAC1E,CAAC,CAAC,sEAAsE,CAAC;YAE7E,KAAK,WAAW;gBACd,OAAO,WAAW;oBAChB,CAAC,CAAC,4FAA4F;oBAC9F,CAAC,CAAC,2DAA2D,CAAC;YAElE,KAAK,gBAAgB;gBACnB,OAAO,WAAW;oBAChB,CAAC,CAAC,8FAA8F;oBAChG,CAAC,CAAC,qEAAqE,CAAC;YAE5E,KAAK,YAAY;gBACf,OAAO,qEAAqE,CAAC;YAE/E;gBACE,OAAO,WAAW;oBAChB,CAAC,CAAC,uCAAuC;oBACzC,CAAC,CAAC,wDAAwD,CAAC;QACjE,CAAC;IACH,CAAC;CACF,CAAA;AAzKY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;GACA,0BAA0B,CAyKtC"}