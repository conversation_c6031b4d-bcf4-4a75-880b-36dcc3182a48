"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedPromptBuilderService = void 0;
const common_1 = require("@nestjs/common");
let SharedPromptBuilderService = class SharedPromptBuilderService {
    getBaseSystemRules() {
        return [
            '🚀 RECOMMEND-FIRST RULE: If you received ANY tool candidates, you MUST recommend 1-3 of them',
            '🚀 Only ask clarifying questions when the candidate list is EMPTY',
            '🎯 CRITICAL: ONLY mention AI tools that are in the "Relevant AI Tools Available" list below',
            '🎯 NEVER invent or hallucinate tool names, IDs, or details not in the provided list',
            '🎯 If no relevant tools are provided, focus on understanding user needs better',
            'Be helpful, concise, and focus on providing value to the user',
            'Always maintain a professional and friendly tone'
        ];
    }
    buildSystemPrompt(context, candidateEntities, additionalRules = []) {
        const baseRules = this.getBaseSystemRules();
        const allRules = [...baseRules, ...additionalRules];
        let systemPrompt = `You are an AI assistant that helps users discover the perfect AI tools for their needs.

CORE RULES:
${allRules.map((rule, index) => `${index + 1}. ${rule}`).join('\n')}

CONVERSATION CONTEXT:
- Current Stage: ${context.conversationStage}
- Session ID: ${context.sessionId}
- Message Count: ${context.messages?.length || 0}`;
        if (candidateEntities && candidateEntities.length > 0) {
            systemPrompt += `

RELEVANT AI TOOLS AVAILABLE (${candidateEntities.length} tools):
${candidateEntities.map((entity, index) => `${index + 1}. **${entity.name}** (ID: ${entity.id})
   - Type: ${entity.type}
   - Description: ${entity.shortDescription}
   - Categories: ${entity.categories?.map(c => c.category.name).join(', ') || 'General'}
   - Features: ${entity.features?.map(f => f.feature.name).join(', ') || 'Various features'}`).join('\n\n')}

Since you have ${candidateEntities.length} relevant tools available, you MUST recommend 1-3 of them that best match the user's needs.`;
        }
        else {
            systemPrompt += `

RELEVANT AI TOOLS AVAILABLE: None found
Since no specific tools were found, focus on understanding the user's needs better to help them find the right solution.`;
        }
        return systemPrompt;
    }
    buildConversationHistory(context) {
        if (!context.messages || context.messages.length === 0) {
            return [];
        }
        return context.messages.map(msg => ({
            role: msg.role,
            content: msg.content
        }));
    }
    buildUserMessage(userMessage, context) {
        return userMessage;
    }
    validateResponseEntities(response, candidateEntities) {
        const issues = [];
        const validEntityNames = new Set(candidateEntities.map(entity => entity.name.toLowerCase()));
        const suspiciousPatterns = [
            /\b(Lumen5|Promo\.com|Canva|Figma|Adobe Firefly|Synthesia|Runway ML|Jasper|Copy\.ai|Writesonic|Grammarly|Notion AI)\b/gi,
            /\b([A-Z][a-z]{3,}(?:[A-Z][a-z]+)*)\s+(AI|Pro|Plus|Studio|Bot|Assistant|Generator|Creator|Maker|Builder)\b/g,
        ];
        for (const pattern of suspiciousPatterns) {
            const matches = response.match(pattern);
            if (matches) {
                for (const match of matches) {
                    if (!validEntityNames.has(match.toLowerCase())) {
                        issues.push(`Potentially hallucinated tool mentioned: "${match}"`);
                    }
                }
            }
        }
        return {
            isValid: issues.length === 0,
            issues
        };
    }
    formatEntitiesForPrompt(entities) {
        if (!entities || entities.length === 0) {
            return 'No specific tools found for this query.';
        }
        return entities.map((entity, index) => {
            const categories = entity.categories?.map(c => c.category.name).join(', ') || 'General';
            const features = entity.features?.map(f => f.feature.name).join(', ') || 'Various features';
            return `${index + 1}. **${entity.name}**
   - Description: ${entity.shortDescription}
   - Categories: ${categories}
   - Features: ${features}
   - Website: ${entity.websiteUrl || 'Not specified'}`;
        }).join('\n\n');
    }
    getStageInstructions(stage, hasEntities) {
        switch (stage) {
            case 'greeting':
                return hasEntities
                    ? 'Welcome the user and immediately present the relevant tools you found.'
                    : 'Welcome the user and ask what kind of AI tools they\'re looking for.';
            case 'discovery':
                return hasEntities
                    ? 'You have found relevant tools - present them immediately instead of asking more questions.'
                    : 'Ask specific questions to understand what the user needs.';
            case 'recommendation':
                return hasEntities
                    ? 'Present the most relevant tools with clear explanations of why they match the user\'s needs.'
                    : 'Explain that no specific tools were found and ask for more details.';
            case 'refinement':
                return 'Help the user compare options or get more specific recommendations.';
            default:
                return hasEntities
                    ? 'Present the relevant tools you found.'
                    : 'Help the user find the right AI tools for their needs.';
        }
    }
};
exports.SharedPromptBuilderService = SharedPromptBuilderService;
exports.SharedPromptBuilderService = SharedPromptBuilderService = __decorate([
    (0, common_1.Injectable)()
], SharedPromptBuilderService);
//# sourceMappingURL=shared-prompt-builder.service.js.map