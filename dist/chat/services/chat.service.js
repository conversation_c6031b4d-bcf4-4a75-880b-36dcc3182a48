"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ChatService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const common_1 = require("@nestjs/common");
const conversation_manager_service_1 = require("./conversation-manager.service");
const chat_error_handler_service_1 = require("./chat-error-handler.service");
const llm_failover_service_1 = require("./llm-failover.service");
const chat_performance_monitor_service_1 = require("./chat-performance-monitor.service");
const chat_cache_service_1 = require("./chat-cache.service");
const question_suggestion_service_1 = require("./question-suggestion.service");
const response_variation_service_1 = require("./response-variation.service");
const entities_service_1 = require("../../entities/entities.service");
const llm_factory_service_1 = require("../../common/llm/services/llm-factory.service");
let ChatService = ChatService_1 = class ChatService {
    constructor(conversationManager, chatErrorHandler, llmFailover, performanceMonitor, cacheService, questionSuggestion, responseVariation, entitiesService, llmService, llmFactoryService) {
        this.conversationManager = conversationManager;
        this.chatErrorHandler = chatErrorHandler;
        this.llmFailover = llmFailover;
        this.performanceMonitor = performanceMonitor;
        this.cacheService = cacheService;
        this.questionSuggestion = questionSuggestion;
        this.responseVariation = responseVariation;
        this.entitiesService = entitiesService;
        this.llmService = llmService;
        this.llmFactoryService = llmFactoryService;
        this.logger = new common_1.Logger(ChatService_1.name);
    }
    async sendMessage(userId, sendChatMessageDto) {
        const startTime = Date.now();
        const requestId = this.performanceMonitor.recordRequestStart(sendChatMessageDto.session_id || 'new');
        this.logger.log(`Processing chat message for user ${userId}, session: ${sendChatMessageDto.session_id || 'new'}, request: ${requestId}`);
        try {
            let context;
            try {
                context = await this.conversationManager.getOrCreateConversation(userId, sendChatMessageDto.session_id);
                this.logger.debug(`Retrieved conversation context for session ${context.sessionId} with ${context.messages?.length || 0} messages`);
            }
            catch (error) {
                this.logger.error('Failed to get/create conversation context', error.stack);
                return this.chatErrorHandler.handleConversationStateError(error, sendChatMessageDto.session_id || `chat_${Date.now()}`, userId);
            }
            if (sendChatMessageDto.user_preferences) {
                try {
                    await this.conversationManager.updateUserPreferences(context.sessionId, sendChatMessageDto.user_preferences);
                }
                catch (error) {
                    this.logger.warn('Failed to update user preferences', error.stack);
                }
            }
            let updatedContext;
            try {
                updatedContext = await this.conversationManager.addMessage(context.sessionId, {
                    role: 'user',
                    content: sendChatMessageDto.message,
                    metadata: sendChatMessageDto.context,
                });
            }
            catch (error) {
                this.logger.error('Failed to add user message to conversation', error.stack);
                updatedContext = context;
            }
            let candidateEntities;
            try {
                console.time('🔍 EntityDiscovery');
                candidateEntities = await this.discoverRelevantEntities(sendChatMessageDto.message, updatedContext);
                console.timeEnd('🔍 EntityDiscovery');
                this.logger.log(`🔍 ENTITY DISCOVERY RESULTS:`, {
                    query: sendChatMessageDto.message,
                    candidateCount: candidateEntities?.length || 0,
                    firstThreeIds: candidateEntities?.slice(0, 3).map(e => e.id) || [],
                    firstThreeNames: candidateEntities?.slice(0, 3).map(e => e.name) || [],
                    sessionId: updatedContext.sessionId
                });
                if (!candidateEntities || candidateEntities.length === 0) {
                    this.logger.warn('🚨 CRITICAL: No entities discovered for query', {
                        query: sendChatMessageDto.message,
                        sessionId: updatedContext.sessionId
                    });
                }
            }
            catch (error) {
                console.timeEnd('🔍 EntityDiscovery');
                this.logger.warn('Entity discovery failed, continuing without entities', error.stack);
                candidateEntities = [];
            }
            let chatResponse;
            try {
                console.time('🤖 LLMProcessing');
                if (updatedContext.conversationStage === 'discovery' && candidateEntities?.length > 0) {
                    this.logger.log(`🎯 Stage override: discovery → recommendation (${candidateEntities.length} candidates available)`);
                    updatedContext.conversationStage = 'recommendation';
                }
                this.logger.log(`🤖 LLM INPUT PAYLOAD:`, {
                    message: sendChatMessageDto.message,
                    candidateEntitiesCount: candidateEntities?.length || 0,
                    candidateEntitiesPresent: !!candidateEntities && candidateEntities.length > 0,
                    firstThreeEntities: candidateEntities?.slice(0, 3).map(e => ({
                        id: e.id,
                        name: e.name,
                        type: e.type
                    })) || [],
                    conversationStage: updatedContext.conversationStage,
                    sessionId: updatedContext.sessionId
                });
                chatResponse = await this.llmFailover.getChatResponseWithFailover(sendChatMessageDto.message, updatedContext, candidateEntities);
                console.timeEnd('🤖 LLMProcessing');
                this.logger.log(`🤖 LLM RESPONSE:`, {
                    hasMessage: !!chatResponse?.message,
                    messageLength: chatResponse?.message?.length || 0,
                    discoveredEntitiesCount: chatResponse?.discoveredEntities?.length || 0,
                    discoveredEntities: chatResponse?.discoveredEntities?.map(e => ({
                        id: e.id,
                        name: e.name,
                        relevanceScore: e.relevanceScore
                    })) || [],
                    shouldTransition: chatResponse?.shouldTransitionToRecommendations,
                    sessionId: updatedContext.sessionId
                });
            }
            catch (error) {
                console.timeEnd('🤖 LLMProcessing');
                this.logger.error('All LLM providers failed', error.stack);
                return this.chatErrorHandler.handleLlmError(error, updatedContext, sendChatMessageDto.message);
            }
            let finalContext = updatedContext;
            try {
                finalContext = await this.conversationManager.addMessage(context.sessionId, {
                    role: 'assistant',
                    content: chatResponse.message,
                    metadata: {
                        intent: chatResponse.intent,
                        llmProvider: chatResponse.metadata.llmProvider,
                        responseTime: chatResponse.metadata.responseTime,
                    },
                }, chatResponse.intent);
            }
            catch (error) {
                this.logger.warn('Failed to add assistant message to conversation', error.stack);
            }
            if (chatResponse.discoveredEntities && chatResponse.discoveredEntities.length > 0) {
                try {
                    const entityIds = chatResponse.discoveredEntities.map(e => e.id);
                    await this.conversationManager.addDiscoveredEntities(context.sessionId, entityIds);
                }
                catch (error) {
                    this.logger.warn('Failed to update discovered entities', error.stack);
                }
            }
            if (chatResponse.conversationStage !== finalContext.conversationStage) {
                try {
                    await this.conversationManager.updateConversationStage(context.sessionId, chatResponse.conversationStage);
                }
                catch (error) {
                    this.logger.warn('Failed to update conversation stage', error.stack);
                }
            }
            let variedResponse = chatResponse;
            this.logger.log(`Using original response without variation for session ${context.sessionId}`);
            let smartFollowUpQuestions = [];
            try {
                smartFollowUpQuestions = this.questionSuggestion.generateFollowUpQuestions(finalContext, sendChatMessageDto.message, 2);
                this.logger.log(`Generated ${smartFollowUpQuestions.length} smart follow-up questions for session ${context.sessionId}`);
            }
            catch (error) {
                this.logger.warn('Failed to generate smart follow-up questions, using LLM fallback', error.stack);
                smartFollowUpQuestions = variedResponse.followUpQuestions || [];
            }
            finalContext = updatedContext;
            const response = {
                message: variedResponse.message,
                session_id: context.sessionId,
                conversation_stage: variedResponse.conversationStage,
                suggested_actions: chatResponse.suggestedActions?.map(action => ({
                    type: action.type,
                    label: action.label,
                    data: action.data,
                })),
                discovered_entities: chatResponse.discoveredEntities?.map(entity => ({
                    id: entity.id,
                    name: entity.name,
                    relevance_score: entity.relevanceScore,
                    reason: entity.reason,
                })),
                follow_up_questions: smartFollowUpQuestions,
                should_transition_to_recommendations: chatResponse.shouldTransitionToRecommendations,
                metadata: {
                    response_time: Date.now() - startTime,
                    llm_provider: chatResponse.metadata.llmProvider,
                    tokens_used: chatResponse.metadata.tokensUsed,
                },
                generated_at: new Date(),
            };
            this.performanceMonitor.recordRequestComplete(requestId, response.metadata.response_time, true, response.metadata.llm_provider, response.conversation_stage);
            this.logger.log(`Chat response generated for session ${context.sessionId} in ${response.metadata.response_time}ms`);
            return response;
        }
        catch (error) {
            this.logger.error('Critical error processing chat message', error.stack);
            this.performanceMonitor.recordRequestComplete(requestId, Date.now() - startTime, false, 'FALLBACK', 'error', error.message);
            return this.chatErrorHandler.createCriticalErrorFallback(sendChatMessageDto.session_id || `chat_${Date.now()}`, error);
        }
    }
    async getConversationHistory(userId, sessionId, getHistoryDto) {
        this.logger.log(`Getting conversation history for session ${sessionId}`);
        try {
            const context = await this.conversationManager.getOrCreateConversation(userId, sessionId);
            if (context.userId !== userId) {
                this.chatErrorHandler.handleAuthError(new Error('Session does not belong to the user'), sessionId);
            }
            const messages = await this.conversationManager.getConversationHistory(sessionId, getHistoryDto.limit);
            const response = {
                session_id: sessionId,
                conversation_stage: context.conversationStage,
                messages: messages.map(msg => ({
                    id: msg.id,
                    role: msg.role,
                    content: msg.content,
                    timestamp: msg.timestamp,
                    metadata: msg.metadata,
                })),
                total_messages: context.metadata.totalMessages,
                discovered_entities_count: context.discoveredEntities.length,
                started_at: context.metadata.startedAt,
                last_active_at: context.metadata.lastActiveAt,
            };
            return response;
        }
        catch (error) {
            this.logger.error('Error getting conversation history', error.stack);
            throw error;
        }
    }
    async endConversation(userId, sessionId) {
        this.logger.log(`Ending conversation session ${sessionId} for user ${userId}`);
        const context = await this.conversationManager.getOrCreateConversation(userId, sessionId);
        if (context.userId !== userId) {
            throw new Error('Session does not belong to the user');
        }
        await this.conversationManager.endConversation(sessionId);
    }
    async getUserActiveSessions(userId) {
        return this.conversationManager.getUserActiveSessions(userId);
    }
    async discoverRelevantEntities(userMessage, context) {
        try {
            const vectorResults = await this.cacheService.getOrSetEntitySearch(userMessage, async () => {
                return Promise.race([
                    this.entitiesService.vectorSearch({
                        query: userMessage,
                        limit: 10,
                    }),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Vector search timeout')), 15000)),
                ]);
            });
            const MIN_RESULTS = 3;
            let candidates = vectorResults;
            if (candidates.length < MIN_RESULTS) {
                this.logger.log(`🔍 Vector recall low (${candidates.length}) ➜ keyword fusion for: "${userMessage}"`);
                try {
                    const keywordResults = await this.entitiesService.findAll({
                        searchTerm: userMessage,
                        limit: 20,
                        page: 1,
                        status: 'ACTIVE'
                    });
                    if (keywordResults.data.length > 0) {
                        this.logger.log(`🔍 Keyword search found ${keywordResults.data.length} entities`);
                        const convertedKeywordResults = keywordResults.data.map(entity => ({
                            id: entity.id,
                            name: entity.name,
                            shortDescription: entity.shortDescription,
                            logoUrl: entity.logoUrl,
                            entityTypeSlug: entity.entityType?.slug,
                            similarity: 0.6
                        }));
                        const mergedResults = this.mergeUniqueEntities(candidates, convertedKeywordResults);
                        candidates = mergedResults.slice(0, 20);
                        this.logger.log(`🔍 Hybrid search: ${vectorResults.length} vector + ${convertedKeywordResults.length} keyword = ${candidates.length} total`);
                    }
                }
                catch (keywordError) {
                    this.logger.error('Keyword search fallback failed', keywordError.stack);
                }
            }
            if (candidates.length === 0) {
                this.logger.error(`🚨 CRITICAL: No entities found for query: "${userMessage}" - both vector and keyword search failed`);
                return [];
            }
            this.logger.debug(`Converting ${candidates.length} search results to full entities`);
            const fullEntities = await Promise.all(candidates.map(async (candidateResult) => {
                try {
                    const fullEntity = await this.entitiesService.findOne(candidateResult.id);
                    if (!fullEntity) {
                        this.logger.warn(`Entity ${candidateResult.id} not found when fetching full data`);
                        return null;
                    }
                    const entityWithRelations = fullEntity;
                    return {
                        id: entityWithRelations.id,
                        name: entityWithRelations.name,
                        shortDescription: entityWithRelations.shortDescription,
                        description: entityWithRelations.description,
                        entityType: {
                            name: entityWithRelations.entityType.name,
                            slug: entityWithRelations.entityType.slug,
                        },
                        categories: entityWithRelations.entityCategories?.map(ec => ec.category) || [],
                        tags: entityWithRelations.entityTags?.map(et => et.tag) || [],
                        features: entityWithRelations.entityFeatures?.map(ef => ef.feature) || [],
                        websiteUrl: entityWithRelations.websiteUrl,
                        logoUrl: entityWithRelations.logoUrl,
                        avgRating: entityWithRelations.avgRating,
                        reviewCount: entityWithRelations.reviewCount,
                    };
                }
                catch (error) {
                    this.logger.warn(`Error fetching full entity data for ${candidateResult.id}`, error.message);
                    return null;
                }
            }));
            const validEntities = fullEntities.filter(entity => entity !== null);
            this.logger.debug(`Successfully converted ${validEntities.length}/${candidates.length} entities to full format`);
            if (validEntities.length === 0) {
                this.logger.warn('No valid entities after conversion - all entities failed to load');
                return [];
            }
            let filteredResults = validEntities;
            try {
                if (context.userPreferences?.excluded_categories?.length > 0) {
                    filteredResults = validEntities.filter(entity => {
                        try {
                            const entityCategories = entity.categories?.map((c) => c.category.name) || [];
                            return !context.userPreferences.excluded_categories.some((excluded) => entityCategories.includes(excluded));
                        }
                        catch (filterError) {
                            this.logger.warn('Error filtering entity by excluded categories', filterError);
                            return true;
                        }
                    });
                }
                if (context.userPreferences?.preferred_categories?.length > 0) {
                    filteredResults = filteredResults.sort((a, b) => {
                        try {
                            const aCategoriesMatch = a.categories?.some((c) => context.userPreferences.preferred_categories.includes(c.category.name)) || false;
                            const bCategoriesMatch = b.categories?.some((c) => context.userPreferences.preferred_categories.includes(c.category.name)) || false;
                            if (aCategoriesMatch && !bCategoriesMatch)
                                return -1;
                            if (!aCategoriesMatch && bCategoriesMatch)
                                return 1;
                            return 0;
                        }
                        catch (sortError) {
                            this.logger.warn('Error sorting entities by preferences', sortError);
                            return 0;
                        }
                    });
                }
            }
            catch (preferencesError) {
                this.logger.warn('Error applying user preferences to entity filtering', preferencesError);
            }
            this.logger.debug(`Found ${filteredResults.length} relevant entities for chat context (after filtering)`);
            return filteredResults.slice(0, 5);
        }
        catch (error) {
            this.logger.error('Error discovering relevant entities', error.stack);
            if (error.message?.includes('database') || error.message?.includes('connection')) {
                throw error;
            }
            return [];
        }
    }
    getPerformanceMetrics() {
        return this.performanceMonitor.getMetrics();
    }
    getPerformanceHealth() {
        return this.performanceMonitor.getHealthStatus();
    }
    getCacheStats() {
        return this.cacheService.getCacheStats();
    }
    clearCaches() {
        this.cacheService.clearAllCaches();
    }
    async getCurrentLlmProvider() {
        try {
            const providers = this.llmFactoryService.getAvailableProviders();
            return providers[0] || 'UNKNOWN';
        }
        catch (error) {
            this.logger.error('Error getting current LLM provider', error.stack);
            return 'UNKNOWN';
        }
    }
    mergeUniqueEntities(vectorResults, keywordResults) {
        const entityMap = new Map();
        vectorResults.forEach(entity => {
            entityMap.set(entity.id, entity);
        });
        keywordResults.forEach(entity => {
            if (!entityMap.has(entity.id)) {
                entityMap.set(entity.id, entity);
            }
        });
        return Array.from(entityMap.values());
    }
};
exports.ChatService = ChatService;
exports.ChatService = ChatService = ChatService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(8, (0, common_1.Inject)('ILlmService')),
    __metadata("design:paramtypes", [conversation_manager_service_1.ConversationManagerService,
        chat_error_handler_service_1.ChatErrorHandlerService,
        llm_failover_service_1.LlmFailoverService,
        chat_performance_monitor_service_1.ChatPerformanceMonitorService,
        chat_cache_service_1.ChatCacheService,
        question_suggestion_service_1.QuestionSuggestionService,
        response_variation_service_1.ResponseVariationService,
        entities_service_1.EntitiesService, Object, llm_factory_service_1.LlmFactoryService])
], ChatService);
//# sourceMappingURL=chat.service.js.map