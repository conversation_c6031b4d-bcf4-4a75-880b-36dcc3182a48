#!/usr/bin/env node

/**
 * Focused test script for chat system fixes
 * Tests the three critical fixes:
 * 1. Hybrid search (vector + keyword fusion)
 * 2. Stage override (discovery → recommendation when candidates exist)
 * 3. Cache key fix (session-specific caching)
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test queries that should trigger recommendations
const TEST_QUERIES = [
  {
    name: 'Background Removal',
    message: 'I need a tool for removing backgrounds from images',
    expectedTools: ['Designify', 'Remove.bg', 'PhotoRoom']
  },
  {
    name: 'Content Creation',
    message: 'An AI to help me create, schedule, and post content for my e-com business',
    expectedTools: ['Predis.ai', 'Copilotly', 'Ocoya']
  },
  {
    name: 'Video Generation',
    message: 'best AI video generation tool',
    expectedTools: ['Runway', 'Pika', 'Synthesia']
  }
];

async function testChatFixes() {
  console.log('🚀 TESTING CHAT SYSTEM FIXES\n');
  
  let passedTests = 0;
  let totalTests = 0;

  for (const testQuery of TEST_QUERIES) {
    console.log(`\n📝 Testing: ${testQuery.name}`);
    console.log(`Query: "${testQuery.message}"`);
    
    try {
      totalTests++;
      
      const response = await axios.post(`${BASE_URL}/chat`, {
        message: testQuery.message
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      const chatResponse = response.data;
      
      // Check if we got a proper response
      if (!chatResponse.message) {
        console.log('❌ FAIL: No message in response');
        continue;
      }

      // Check if response contains fallback patterns (indicates failure)
      const message = chatResponse.message.toLowerCase();
      const isFallback = message.includes('i understand you\'re looking') ||
                        message.includes('what specific features') ||
                        message.includes('i apologize') ||
                        message.includes('let me help you find');

      if (isFallback) {
        console.log('❌ FAIL: Got fallback/discovery message instead of recommendations');
        console.log(`Response: ${chatResponse.message.substring(0, 100)}...`);
        continue;
      }

      // Check if we got tool recommendations
      const hasRecommendations = chatResponse.discoveredEntities && 
                                chatResponse.discoveredEntities.length > 0;

      if (!hasRecommendations) {
        console.log('❌ FAIL: No tool recommendations in response');
        console.log(`Response: ${chatResponse.message.substring(0, 100)}...`);
        continue;
      }

      // Check if recommended tools are relevant
      const recommendedTools = chatResponse.discoveredEntities.map(e => e.name);
      const hasRelevantTool = testQuery.expectedTools.some(expected => 
        recommendedTools.some(recommended => 
          recommended.toLowerCase().includes(expected.toLowerCase()) ||
          expected.toLowerCase().includes(recommended.toLowerCase())
        )
      );

      if (hasRelevantTool) {
        console.log('✅ PASS: Got relevant tool recommendations');
        console.log(`Recommended: ${recommendedTools.join(', ')}`);
        passedTests++;
      } else {
        console.log('⚠️  PARTIAL: Got recommendations but not the expected ones');
        console.log(`Expected: ${testQuery.expectedTools.join(', ')}`);
        console.log(`Got: ${recommendedTools.join(', ')}`);
        // Still count as partial success since we got recommendations
        passedTests += 0.5;
      }

    } catch (error) {
      console.log('❌ ERROR:', error.message);
      if (error.response) {
        console.log('Response status:', error.response.status);
        console.log('Response data:', JSON.stringify(error.response.data, null, 2));
      }
    }
  }

  console.log('\n📊 TEST RESULTS');
  console.log(`Passed: ${passedTests}/${totalTests}`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

  if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED! Chat system is working correctly.');
  } else if (passedTests > totalTests * 0.5) {
    console.log('⚠️  PARTIAL SUCCESS: Some improvements but still needs work.');
  } else {
    console.log('❌ TESTS FAILED: Chat system still has issues.');
  }

  return passedTests === totalTests;
}

// Test cache key fix by making the same request twice
async function testCacheKeyFix() {
  console.log('\n🔄 TESTING CACHE KEY FIX (Session Isolation)');
  
  const testMessage = 'I need AI for video editing';
  
  try {
    // Make two identical requests
    const [response1, response2] = await Promise.all([
      axios.post(`${BASE_URL}/chat`, { message: testMessage }),
      axios.post(`${BASE_URL}/chat`, { message: testMessage })
    ]);

    // Check if responses are identical (would indicate cache issue)
    const message1 = response1.data.message;
    const message2 = response2.data.message;
    
    if (message1 === message2) {
      console.log('⚠️  WARNING: Identical responses detected - cache key may still be an issue');
      console.log('This could be normal if the LLM generates consistent responses');
    } else {
      console.log('✅ PASS: Different responses for identical queries (good cache isolation)');
    }
    
  } catch (error) {
    console.log('❌ ERROR testing cache:', error.message);
  }
}

async function main() {
  console.log('🔧 FOCUSED CHAT SYSTEM FIX VERIFICATION');
  console.log('Testing the three critical fixes implemented:\n');
  console.log('1. 🔍 Hybrid Search (vector + keyword fusion)');
  console.log('2. 🎯 Stage Override (force recommendations when candidates exist)');
  console.log('3. 🔑 Cache Key Fix (session-specific caching)');
  console.log('4. 🚀 Recommend-First Rule (prioritize recommendations over questions)\n');

  // Test main chat functionality
  const success = await testChatFixes();
  
  // Test cache isolation
  await testCacheKeyFix();

  console.log('\n' + '='.repeat(60));
  if (success) {
    console.log('🎯 MISSION ACCOMPLISHED: Chat system fixes are working!');
    console.log('Users should now get tool recommendations instead of repetitive questions.');
  } else {
    console.log('🔧 NEEDS MORE WORK: Some issues remain in the chat system.');
    console.log('Check the logs above for specific failures.');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testChatFixes, testCacheKeyFix };
